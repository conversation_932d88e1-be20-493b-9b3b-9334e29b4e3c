"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader2, Save, GraduationCap } from "lucide-react"

const classFormSchema = z.object({
  class_id: z.string().min(1, "ID kelas wajib diisi"),
  name: z.string().min(1, "Nama kelas wajib diisi"),
  level: z.string().min(1, "Tingkat wajib dipilih"),
  academic_year: z.string().min(1, "Tahun ajaran wajib diisi"),
  homeroom_teacher_id: z.string().optional(),
})

// Schema untuk edit mode - class_id tidak wajib diubah
const editClassFormSchema = z.object({
  class_id: z.string().optional(),
  name: z.string().min(1, "Nama kelas wajib diisi"),
  level: z.string().min(1, "Tingkat wajib dipilih"),
  academic_year: z.string().min(1, "Tahun ajaran wajib diisi"),
  homeroom_teacher_id: z.string().optional(),
})

type ClassFormValues = z.infer<typeof classFormSchema>

interface ClassFormProps {
  classId?: string
}

export function ClassForm({ classId }: ClassFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(!!classId)
  const [teachers, setTeachers] = useState<Array<{ id: string; name: string; email: string }>>([])
  
  const isEditMode = !!classId

  const form = useForm<ClassFormValues>({
    resolver: zodResolver(isEditMode ? editClassFormSchema : classFormSchema),
    defaultValues: {
      class_id: "",
      name: "",
      level: "",
      academic_year: "",
      homeroom_teacher_id: "",
    },
  })

  // Fetch teachers and class data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch teachers
        const teachersResponse = await fetch('/api/teachers')
        if (teachersResponse.ok) {
          const teachersData = await teachersResponse.json()
          setTeachers(teachersData.teachers || [])
        }

        // Fetch class data for edit mode
        if (isEditMode && classId) {
          setLoadingData(true)
          console.log("Fetching class data for:", classId)

          try {
            const response = await fetch(`/api/classes?id=${classId}`)
            if (response.ok) {
              const data = await response.json()
              console.log("API response:", data)

              // Handle different response formats
              const cls = data.class || data.classes?.[0] || data

              console.log("Loaded class data:", cls)

              if (cls && cls.class_id) {
                // Populate form with class data
                form.setValue("class_id", cls.class_id || "")
                form.setValue("name", cls.name || "")
                form.setValue("level", cls.level || "")
                form.setValue("academic_year", cls.academic_year || "")
                form.setValue("homeroom_teacher_id", cls.homeroom_teacher_id || "")
              } else {
                console.error("Class data not found in response:", data)
              }
            } else {
              console.error("Failed to fetch class data:", response.status)
            }
          } catch (error) {
            console.error("Error fetching class data:", error)
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoadingData(false)
      }
    }

    fetchData()
  }, [isEditMode, classId, form])

  const onSubmit = async (values: ClassFormValues) => {
    setLoading(true)
    try {
      console.log("Submitting class form:", values)

      // Handle "none" value for homeroom_teacher_id
      const processedValues = {
        ...values,
        homeroom_teacher_id: values.homeroom_teacher_id === "none" ? null : values.homeroom_teacher_id
      }

      const url = isEditMode ? `/api/classes?id=${classId}` : '/api/classes'
      const method = isEditMode ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processedValues),
      })

      if (response.ok) {
        const result = await response.json()
        console.log("Class form submitted successfully:", result)
        
        if (isEditMode) {
          router.push(`/dashboard/classes/${classId}`)
        } else {
          router.push('/dashboard/classes')
        }
      } else {
        const error = await response.json()
        console.error("Failed to submit class form:", error)
        alert(`Gagal ${isEditMode ? 'mengupdate' : 'menambahkan'} data kelas: ${error.error}`)
      }
    } catch (error) {
      console.error("Error submitting class form:", error)
      alert(`Terjadi kesalahan saat ${isEditMode ? 'mengupdate' : 'menambahkan'} data kelas`)
    } finally {
      setLoading(false)
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat data kelas...</span>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                {isEditMode ? "Edit Data Kelas" : "Tambah Kelas Baru"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="class_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ID Kelas {!isEditMode && "*"}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Contoh: 7A, 8B, 9C"
                          disabled={isEditMode}
                          {...field}
                        />
                      </FormControl>
                      {isEditMode && (
                        <p className="text-sm text-muted-foreground">
                          ID kelas tidak dapat diubah saat edit
                        </p>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tingkat *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih tingkat" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SD">SD</SelectItem>
                          <SelectItem value="SMP">SMP</SelectItem>
                          <SelectItem value="SMA">SMA</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Kelas *</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: Kelas 7A, Kelas 8 Unggulan" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="academic_year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tahun Ajaran *</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: 2024/2025" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="homeroom_teacher_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Wali Kelas</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih wali kelas (opsional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">Belum ditentukan</SelectItem>
                        {teachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
              className="flex-1"
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading} className="flex-1">
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              {isEditMode ? "Update Kelas" : "Simpan Kelas"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
