"use client"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface SafeAvatarProps {
  src?: string | null
  alt: string
  fallbackText: string
  className?: string
  fallbackClassName?: string
  size?: "sm" | "md" | "lg" | "xl"
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-12 w-12", 
  lg: "h-16 w-16",
  xl: "h-24 w-24"
}

const fallbackSizes = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-lg", 
  xl: "text-xl"
}

export function SafeAvatar({ 
  src, 
  alt, 
  fallbackText, 
  className,
  fallbackClassName,
  size = "md"
}: SafeAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // Generate fallback placeholder URL based on size
  const getPlaceholderUrl = (size: string) => {
    const dimensions = {
      sm: "32",
      md: "48", 
      lg: "64",
      xl: "96"
    }
    const dim = dimensions[size as keyof typeof dimensions] || "48"
    return `/placeholder.svg?height=${dim}&width=${dim}`
  }

  // Determine what to show
  const shouldShowImage = src && !imageError && src.trim() !== ""
  const imageUrl = shouldShowImage ? src : getPlaceholderUrl(size)

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage 
        src={imageUrl}
        alt={alt}
        onLoad={() => setImageLoaded(true)}
        onError={() => setImageError(true)}
        style={{
          display: imageError ? 'none' : 'block'
        }}
      />
      <AvatarFallback className={cn(fallbackSizes[size], fallbackClassName)}>
        {fallbackText}
      </AvatarFallback>
    </Avatar>
  )
}

// Utility function to generate initials from name
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2) // Limit to 2 characters
}

// Pre-configured variants for common use cases
export function TeacherAvatar({ 
  teacher, 
  size = "md", 
  className 
}: { 
  teacher: { name: string; photo_url?: string | null }
  size?: "sm" | "md" | "lg" | "xl"
  className?: string 
}) {
  return (
    <SafeAvatar
      src={teacher.photo_url}
      alt={teacher.name}
      fallbackText={getInitials(teacher.name)}
      size={size}
      className={className}
    />
  )
}

export function StudentAvatar({ 
  student, 
  size = "md", 
  className 
}: { 
  student: { name: string; photo_url?: string | null }
  size?: "sm" | "md" | "lg" | "xl"
  className?: string 
}) {
  return (
    <SafeAvatar
      src={student.photo_url}
      alt={student.name}
      fallbackText={getInitials(student.name)}
      size={size}
      className={className}
    />
  )
}
