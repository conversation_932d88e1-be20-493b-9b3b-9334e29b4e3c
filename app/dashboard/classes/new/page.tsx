import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { ClassForm } from "@/components/classes/class-form"

export default function NewClassPage() {
  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/classes">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tambah Kelas Baru</h1>
          <p className="text-muted-foreground">Buat kelas baru dan tentukan wali kelas</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <Suspense fallback={<div>Loading...</div>}>
        <ClassForm />
      </Suspense>
    </div>
  )
}
