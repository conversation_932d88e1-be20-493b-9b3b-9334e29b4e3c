import { Suspense } from "react"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TeacherAvatar } from "@/components/ui/safe-avatar"
import { 
  ArrowLeft, 
  Edit, 
  Mail, 
  UserCheck, 
  GraduationCap, 
  Calendar,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { createServerClient } from "@/utils/supabase/server"

// Function to get teacher data
async function getTeacher(teacherId: string) {
  try {
    const supabase = await createServerClient()

    const { data: teacher, error } = await supabase
      .from('teachers')
      .select(`
        id,
        teacher_id,
        name,
        specialization,
        photo_url,
        join_date,
        status,
        created_at,
        updated_at
      `)
      .eq('id', teacherId)
      .single()

    if (error) {
      console.error('Error fetching teacher:', error)
      return null
    }

    return teacher
  } catch (error) {
    console.error('Failed to get teacher:', error)
    return null
  }
}

// Function to get teacher's classes
async function getTeacherClasses(teacherId: string) {
  try {
    const supabase = await createServerClient()

    const { data: classes, error } = await supabase
      .from('classes')
      .select(`
        id,
        class_id,
        name,
        level,
        academic_year
      `)
      .eq('homeroom_teacher_id', teacherId)

    if (error) {
      console.error('Error fetching teacher classes:', error)
      return []
    }

    return classes || []
  } catch (error) {
    console.error('Failed to get teacher classes:', error)
    return []
  }
}

interface TeacherDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function TeacherDetailPage({ params }: TeacherDetailPageProps) {
  const { id } = await params
  
  console.log("TeacherDetailPage - Teacher ID:", id)

  // Fetch teacher data and classes
  const [teacher, classes] = await Promise.all([
    getTeacher(id),
    getTeacherClasses(id)
  ])

  console.log("TeacherDetailPage - Teacher data:", teacher)
  console.log("TeacherDetailPage - Teacher classes:", classes)

  if (!teacher) {
    notFound()
  }

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'sd': return 'bg-green-100 text-green-800'
      case 'smp': return 'bg-blue-100 text-blue-800'
      case 'sma': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/teachers">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detail Guru</h1>
            <p className="text-muted-foreground">Informasi lengkap guru dan kelas yang diampu</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/dashboard/teachers/${id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Data
          </Link>
        </Button>
      </div>

      <Separator className="mb-6" />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Teacher Info */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <TeacherAvatar teacher={teacher} size="lg" />
                <div>
                  <CardTitle className="text-2xl">{teacher.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="default">
                      {teacher.teacher_id}
                    </Badge>
                    {teacher.specialization && (
                      <Badge variant="secondary">
                        {teacher.specialization}
                      </Badge>
                    )}
                    {classes.length > 0 && (
                      <Badge variant="outline">
                        Wali {classes.length} Kelas
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">ID Guru</p>
                    <p className="text-sm text-muted-foreground">{teacher.teacher_id}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <UserCheck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Spesialisasi</p>
                    <p className="text-sm text-muted-foreground">{teacher.specialization || "Belum ada"}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <GraduationCap className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Jumlah Kelas</p>
                    <p className="text-sm text-muted-foreground">{classes.length} kelas</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Terdaftar</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(teacher.created_at), { 
                        addSuffix: true
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Classes List */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Kelas yang Diampu ({classes.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {classes.length > 0 ? (
                <div className="space-y-3">
                  {classes.map((cls) => (
                    <div key={cls.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <GraduationCap className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{cls.name}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge className={getLevelColor(cls.level)} variant="secondary">
                              {cls.level}
                            </Badge>
                            <span>{cls.academic_year}</span>
                          </div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/classes/${cls.id}`}>
                          <BookOpen className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Belum mengampu kelas</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Guru ini belum ditugaskan sebagai wali kelas
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
