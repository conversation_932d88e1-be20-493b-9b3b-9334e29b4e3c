import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"
import * as XLSX from 'xlsx'

// POST - Import students with parents from Excel
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: "File is required" },
        { status: 400 }
      )
    }

    // Read Excel file
    const buffer = await file.arrayBuffer()
    const workbook = XLSX.read(buffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const data = XLSX.utils.sheet_to_json(worksheet)

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "Excel file is empty or invalid" },
        { status: 400 }
      )
    }

    // Get existing classes for validation
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name, class_id')

    if (classesError) {
      return NextResponse.json(
        { error: classesError.message },
        { status: 500 }
      )
    }

    // Create class mapping
    const classMap = classes?.reduce((acc, cls) => {
      acc[cls.name.toLowerCase()] = cls.id
      acc[cls.class_id.toLowerCase()] = cls.id
      return acc
    }, {} as Record<string, string>) || {}

    const results = []
    const errors = []

    // Process each row in Excel
    for (let i = 0; i < data.length; i++) {
      const row = data[i] as any
      
      try {
        // Extract student data
        const rawGender = row['Jenis Kelamin'] || row['gender']

        // Map gender values to standardized format
        const normalizeGender = (gender: any): string | null => {
          if (!gender) return null
          const genderStr = String(gender).toLowerCase().trim()

          // Map various gender formats to 'male' or 'female'
          if (genderStr === 'l' || genderStr === 'laki-laki' || genderStr === 'male' || genderStr === 'pria') {
            return 'male'
          } else if (genderStr === 'p' || genderStr === 'perempuan' || genderStr === 'female' || genderStr === 'wanita') {
            return 'female'
          }
          return null // Invalid gender will be handled by validation
        }

        const studentData = {
          student_id: row['ID Santri'] || row['student_id'] || `STD-${Date.now()}-${i}`,
          name: row['Nama Santri'] || row['nama'] || row['name'],
          birth_date: row['Tanggal Lahir'] || row['birth_date'],
          birth_place: row['Tempat Lahir'] || row['birth_place'],
          address: row['Alamat'] || row['address'],
          phone: row['No HP Santri'] || row['phone'],
          email: row['Email Santri'] || row['email'],
          gender: normalizeGender(rawGender),
          entry_date: row['Tanggal Masuk'] || row['entry_date'] || new Date().toISOString().split('T')[0],
          status: row['Status'] || row['status'] || 'active'
        }

        // Validate required fields
        if (!studentData.name) {
          errors.push(`Row ${i + 2}: Nama Santri is required`)
          continue
        }

        // Validate gender if provided
        if (rawGender && !studentData.gender) {
          errors.push(`Row ${i + 2}: Invalid gender value '${rawGender}'. Use: L/Laki-laki/Male or P/Perempuan/Female`)
          continue
        }

        // Parse birth_date if it's a string
        if (studentData.birth_date && typeof studentData.birth_date === 'string') {
          const dateFormats = [
            /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
            /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
            /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
          ]
          
          let validDate = null
          for (const format of dateFormats) {
            if (format.test(studentData.birth_date)) {
              if (studentData.birth_date.includes('/')) {
                const [day, month, year] = studentData.birth_date.split('/')
                validDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
              } else if (studentData.birth_date.includes('-') && !studentData.birth_date.startsWith('20')) {
                const [day, month, year] = studentData.birth_date.split('-')
                validDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
              } else {
                validDate = studentData.birth_date
              }
              break
            }
          }
          studentData.birth_date = validDate
        }

        // Insert student
        const { data: student, error: studentError } = await supabase
          .from('students')
          .insert(studentData)
          .select()
          .single()

        if (studentError) {
          errors.push(`Row ${i + 2}: Failed to insert student - ${studentError.message}`)
          continue
        }

        // Process parents data
        const parents = []
        
        // Father data
        const fatherData = {
          name: row['Nama Ayah'] || row['father_name'],
          phone: row['No HP Ayah'] || row['father_phone'],
          email: row['Email Ayah'] || row['father_email'],
          occupation: row['Pekerjaan Ayah'] || row['father_occupation'],
          address: row['Alamat Ayah'] || row['father_address'] || studentData.address
        }

        if (fatherData.name) {
          const { data: father, error: fatherError } = await supabase
            .from('parents')
            .insert(fatherData)
            .select()
            .single()

          if (!fatherError && father) {
            parents.push({
              parent_id: father.id,
              relationship: 'father',
              is_primary: true
            })
          }
        }

        // Mother data
        const motherData = {
          name: row['Nama Ibu'] || row['mother_name'],
          phone: row['No HP Ibu'] || row['mother_phone'],
          email: row['Email Ibu'] || row['mother_email'],
          occupation: row['Pekerjaan Ibu'] || row['mother_occupation'],
          address: row['Alamat Ibu'] || row['mother_address'] || studentData.address
        }

        if (motherData.name) {
          const { data: mother, error: motherError } = await supabase
            .from('parents')
            .insert(motherData)
            .select()
            .single()

          if (!motherError && mother) {
            parents.push({
              parent_id: mother.id,
              relationship: 'mother',
              is_primary: parents.length === 0
            })
          }
        }

        // Guardian data (if different from parents)
        const guardianData = {
          name: row['Nama Wali'] || row['guardian_name'],
          phone: row['No HP Wali'] || row['guardian_phone'],
          email: row['Email Wali'] || row['guardian_email'],
          occupation: row['Pekerjaan Wali'] || row['guardian_occupation'],
          address: row['Alamat Wali'] || row['guardian_address'] || studentData.address
        }

        if (guardianData.name && guardianData.name !== fatherData.name && guardianData.name !== motherData.name) {
          const { data: guardian, error: guardianError } = await supabase
            .from('parents')
            .insert(guardianData)
            .select()
            .single()

          if (!guardianError && guardian) {
            parents.push({
              parent_id: guardian.id,
              relationship: 'guardian',
              is_primary: parents.length === 0
            })
          }
        }

        // Insert student-parent relationships
        for (const parentRel of parents) {
          await supabase
            .from('student_parent')
            .insert({
              student_id: student.id,
              parent_id: parentRel.parent_id,
              relationship: parentRel.relationship,
              is_primary: parentRel.is_primary
            })
        }

        // Assign to class if specified
        const className = row['Kelas'] || row['class'] || row['class_name']
        if (className) {
          const classId = classMap[className.toLowerCase()]
          if (classId) {
            await supabase
              .from('class_students')
              .insert({
                class_id: classId,
                student_id: student.id
              })
          } else {
            errors.push(`Row ${i + 2}: Class '${className}' not found`)
          }
        }

        results.push({
          student_id: student.id,
          student_name: studentData.name,
          parents_count: parents.length,
          class_assigned: !!className && !!classMap[className?.toLowerCase()],
          status: 'success'
        })

      } catch (error: any) {
        errors.push(`Row ${i + 2}: ${error.message}`)
      }
    }

    return NextResponse.json({ 
      message: "Import completed",
      results,
      errors,
      summary: {
        total_rows: data.length,
        successful: results.length,
        failed: errors.length,
        students_created: results.length,
        parents_created: results.reduce((sum, r) => sum + r.parents_count, 0)
      }
    })

  } catch (error: any) {
    console.error('Error in students import:', error)
    return NextResponse.json(
      { error: error.message || "Failed to import students data" },
      { status: 500 }
    )
  }
}
