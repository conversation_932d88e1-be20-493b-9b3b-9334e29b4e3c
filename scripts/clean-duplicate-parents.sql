-- Script untuk membersihkan data parent yang duplikat
-- Jalankan script ini di Supabase SQL Editor

-- 1. Periksa parent yang duplikat berdasarkan nama dan phone
SELECT 'Checking for duplicate parents...' as info;

WITH duplicate_parents AS (
  SELECT 
    name, 
    phone,
    COUNT(*) as count,
    STRING_AGG(id::text, ', ') as parent_ids
  FROM parents 
  WHERE name IS NOT NULL
  GROUP BY name, phone 
  HAVING COUNT(*) > 1
)
SELECT 
  name,
  phone,
  count as duplicate_count,
  parent_ids
FROM duplicate_parents
ORDER BY count DESC;

-- 2. Identifikasi parent yang akan di<PERSON><PERSON> (keep the first one, delete the rest)
WITH duplicate_parents AS (
  SELECT 
    id,
    name,
    phone,
    ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
  FROM parents 
  WHERE name IS NOT NULL
),
parents_to_delete AS (
  SELECT id, name, phone
  FROM duplicate_parents 
  WHERE rn > 1
)
SELECT 'Parents to be deleted:' as info;

SELECT 
  id,
  name,
  phone
FROM parents 
WHERE id IN (
  SELECT id FROM (
    WITH duplicate_parents AS (
      SELECT 
        id,
        name,
        phone,
        ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
      FROM parents 
      WHERE name IS NOT NULL
    )
    SELECT id
    FROM duplicate_parents 
    WHERE rn > 1
  ) as subquery
);

-- 3. Update student_parent relationships untuk mengarah ke parent yang akan dipertahankan
-- Uncomment untuk menjalankan cleanup
/*
WITH duplicate_parents AS (
  SELECT 
    id,
    name,
    phone,
    ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
  FROM parents 
  WHERE name IS NOT NULL
),
parent_mapping AS (
  SELECT 
    d1.id as old_parent_id,
    d2.id as new_parent_id
  FROM duplicate_parents d1
  JOIN duplicate_parents d2 ON d1.name = d2.name AND d1.phone = d2.phone
  WHERE d1.rn > 1 AND d2.rn = 1
)
UPDATE student_parent 
SET parent_id = pm.new_parent_id
FROM parent_mapping pm
WHERE student_parent.parent_id = pm.old_parent_id;

-- 4. Hapus parent yang duplikat
WITH duplicate_parents AS (
  SELECT 
    id,
    name,
    phone,
    ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
  FROM parents 
  WHERE name IS NOT NULL
)
DELETE FROM parents 
WHERE id IN (
  SELECT id 
  FROM duplicate_parents 
  WHERE rn > 1
);
*/

-- 5. Verifikasi hasil setelah cleanup
SELECT 'Verification after cleanup:' as info;

WITH duplicate_check AS (
  SELECT 
    name, 
    phone,
    COUNT(*) as count
  FROM parents 
  WHERE name IS NOT NULL
  GROUP BY name, phone 
  HAVING COUNT(*) > 1
)
SELECT 
  CASE 
    WHEN COUNT(*) = 0 THEN 'No duplicate parents found - cleanup successful!'
    ELSE CONCAT('Still have ', COUNT(*), ' duplicate parent groups')
  END as result
FROM duplicate_check;

SELECT 'Duplicate parent cleanup script completed.' as final_result;
