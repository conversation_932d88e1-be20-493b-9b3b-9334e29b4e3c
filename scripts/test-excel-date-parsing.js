// Script untuk test parsing tanggal Excel
// Jalankan dengan: node scripts/test-excel-date-parsing.js

// Fungsi untuk convert Excel serial number ke tanggal
function parseExcelDate(serialNumber) {
  // Excel serial date starts from 1900-01-01 (but Excel incorrectly treats 1900 as leap year)
  // So we need to subtract 2 days to account for the leap year bug and 0-based indexing
  const excelEpoch = new Date(1900, 0, 1) // January 1, 1900
  const date = new Date(excelEpoch.getTime() + (serialNumber - 2) * 24 * 60 * 60 * 1000)
  return date.toISOString().split('T')[0]
}

// Fungsi parsing tanggal lengkap (sama seperti di import script)
function parseBirthDate(dateValue) {
  if (!dateValue) return null
  
  // If it's already a Date object, format it
  if (dateValue instanceof Date) {
    return dateValue.toISOString().split('T')[0]
  }
  
  const dateStr = String(dateValue).trim()
  
  // Handle Excel serial number (like 41142)
  if (/^\d{5}$/.test(dateStr)) {
    const serialNumber = parseInt(dateStr)
    return parseExcelDate(serialNumber)
  }
  
  // Handle standard date formats
  const dateFormats = [
    /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
    /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
    /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
    /^\d{1,2}\/\d{1,2}\/\d{4}$/, // D/M/YYYY or DD/MM/YYYY
    /^\d{1,2}-\d{1,2}-\d{4}$/, // D-M-YYYY or DD-MM-YYYY
  ]
  
  for (const format of dateFormats) {
    if (format.test(dateStr)) {
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/')
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
      } else if (dateStr.includes('-') && !dateStr.startsWith('20')) {
        const [day, month, year] = dateStr.split('-')
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
      } else {
        return dateStr // Already in YYYY-MM-DD format
      }
    }
  }
  
  return null // Invalid date format
}

// Test cases
const testCases = [
  '41142', // Excel serial number
  '44927', // Another Excel serial number
  '15/08/2010', // DD/MM/YYYY
  '15-08-2010', // DD-MM-YYYY
  '2010-08-15', // YYYY-MM-DD
  '5/3/2010', // D/M/YYYY
  '5-3-2010', // D-M-YYYY
  'invalid', // Invalid format
  '', // Empty
  null, // Null
]

console.log('=== Test Parsing Tanggal Excel ===\n')

testCases.forEach((testCase, index) => {
  const result = parseBirthDate(testCase)
  console.log(`Test ${index + 1}: "${testCase}" -> "${result}"`)
  
  // Jika berhasil parsing, coba buat Date object untuk validasi
  if (result) {
    const dateObj = new Date(result)
    const isValid = !isNaN(dateObj.getTime())
    console.log(`  Valid Date: ${isValid ? 'Yes' : 'No'}`)
    if (isValid) {
      console.log(`  Readable: ${dateObj.toLocaleDateString('id-ID')}`)
    }
  }
  console.log('')
})

// Test khusus untuk serial number yang bermasalah
console.log('=== Test Serial Number Khusus ===\n')
const problematicSerials = [41142, 44927, 36526, 40179]

problematicSerials.forEach(serial => {
  const result = parseExcelDate(serial)
  const dateObj = new Date(result)
  console.log(`Serial ${serial} -> ${result} (${dateObj.toLocaleDateString('id-ID')})`)
})

console.log('\n=== Selesai ===')
console.log('Jika ada serial number yang tidak sesuai, periksa kembali rumus konversi Excel.')
