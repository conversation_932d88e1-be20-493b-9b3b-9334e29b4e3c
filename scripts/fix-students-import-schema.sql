-- Script untuk memperbaiki masalah import students
-- Menambahkan kolom yang hilang di tabel students
-- Jalankan script ini di Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON> struktur tabel students saat ini
SELECT 'Current students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

-- 2. Tambahkan kolom birth_place jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS birth_place VARCHAR;

-- 3. Tambahkan kolom phone jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS phone VARCHAR;

-- 4. Tambahkan kolom email jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS email VARCHAR;

-- 5. Tambahkan kolom entry_date jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS entry_date DATE;

-- 6. <PERSON><PERSON><PERSON> struktur tabel students setelah update
SELECT 'Updated students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

-- 7. Test insert untuk memastikan semua kolom berfungsi
-- Uncomment baris di bawah untuk test
/*
INSERT INTO students (
    student_id, 
    name, 
    birth_date, 
    birth_place, 
    address, 
    phone, 
    email, 
    gender, 
    entry_date, 
    status
) VALUES (
    'TEST-IMPORT-001',
    'Test Student Import',
    '2010-01-01',
    'Jakarta',
    'Test Address',
    '081234567890',
    '<EMAIL>',
    'L',
    '2024-01-01',
    'active'
) ON CONFLICT (student_id) DO UPDATE SET
    name = EXCLUDED.name,
    updated_at = NOW();

-- Hapus data test
DELETE FROM students WHERE student_id = 'TEST-IMPORT-001';
*/

SELECT 'Schema fix completed. You can now retry the student import.' as result;
