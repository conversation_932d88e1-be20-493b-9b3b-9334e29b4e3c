-- <PERSON><PERSON>t untuk memperbaiki constraint gender di tabel students
-- Jalankan script ini di Supabase SQL Editor

-- 1. Periksa constraint yang ada
SELECT 'Checking existing constraints...' as info;
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'students'::regclass 
AND conname LIKE '%gender%';

-- 2. Hapus constraint gender yang ada (jika ada)
DO $$
BEGIN
    -- Coba hapus constraint dengan nama yang mungkin
    BEGIN
        ALTER TABLE students DROP CONSTRAINT IF EXISTS students_gender_check;
        RAISE NOTICE 'Dropped students_gender_check constraint if it existed';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'No students_gender_check constraint to drop';
    END;
    
    BEGIN
        ALTER TABLE students DROP CONSTRAINT IF EXISTS check_gender;
        RAISE NOTICE 'Dropped check_gender constraint if it existed';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'No check_gender constraint to drop';
    END;
END $$;

-- 3. Buat constraint baru yang hanya menerima 'male' dan 'female' atau NULL
ALTER TABLE students ADD CONSTRAINT students_gender_check 
CHECK (gender IN ('male', 'female') OR gender IS NULL);

-- 4. Periksa constraint yang baru dibuat
SELECT 'New constraint created:' as info;
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'students'::regclass 
AND conname = 'students_gender_check';

-- 5. Test insert untuk memastikan constraint berfungsi
-- Test dengan nilai yang valid
INSERT INTO students (
    student_id, 
    name, 
    gender, 
    status
) VALUES (
    'TEST-GENDER-VALID-001',
    'Test Male Student',
    'male',
    'active'
), (
    'TEST-GENDER-VALID-002',
    'Test Female Student',
    'female',
    'active'
), (
    'TEST-GENDER-VALID-003',
    'Test Null Gender Student',
    NULL,
    'active'
) ON CONFLICT (student_id) DO UPDATE SET
    name = EXCLUDED.name,
    gender = EXCLUDED.gender,
    updated_at = NOW();

-- 6. Lihat hasil test
SELECT 'Test results:' as info;
SELECT student_id, name, gender 
FROM students 
WHERE student_id LIKE 'TEST-GENDER-VALID%'
ORDER BY student_id;

-- 7. Hapus data test
DELETE FROM students WHERE student_id LIKE 'TEST-GENDER-VALID%';

-- 8. Test dengan nilai yang tidak valid (ini akan gagal - itu yang diharapkan)
-- Uncomment untuk test error
/*
INSERT INTO students (student_id, name, gender, status) 
VALUES ('TEST-INVALID', 'Test Invalid', 'L', 'active');
*/

SELECT 'Gender constraint fix completed. Only "male", "female", or NULL are now allowed.' as result;
