# Verifikasi Perbaikan Error - Panduan Test

## Masalah yang Diperbaiki

### 1. ✅ **Error Fetching Homeroom Teacher**
**Masalah**: Query mengambil dari tabel `users` padahal se<PERSON>nya `teachers`
**Perbaikan**: Mengubah query dari `users` ke `teachers` table

### 2. ✅ **Error <PERSON><PERSON>ian Orang <PERSON>a - toLowerCase()**
**Masalah**: `Cannot read properties of null (reading 'toLowerCase')`
**Perbaikan**: Menambahkan null check dengan `|| ''` sebelum `toLowerCase()`

### 3. ✅ **Menu Tambah Kelas Tidak Bisa Diklik**
**Masalah**: Halaman `/dashboard/classes/new` tidak ada
**Perbaikan**: Membuat halaman `app/dashboard/classes/new/page.tsx`

## Test Verifikasi

### 1. **Test Homeroom Teacher di Detail Kelas**

**Langkah:**
1. Buka `/dashboard/classes`
2. <PERSON><PERSON>h salah satu kelas
3. <PERSON><PERSON> "Detail"
4. <PERSON><PERSON><PERSON>ian "W<PERSON>"

**Hasil yang diharapkan:**
- ✅ Tidak ada error di console: `Error fetching homeroom teacher: {}`
- ✅ Nama wali kelas tampil dengan benar (jika ada)
- ✅ Tampil "Belum ditentukan" jika tidak ada wali kelas

**Debug jika masih error:**
```javascript
// Cek di browser console
console.log("Homeroom teacher data:", homeroomTeacher)
```

### 2. **Test Pencarian Orang Tua**

**Langkah:**
1. Buka `/dashboard/parents`
2. Ketik sesuatu di search box
3. Coba search dengan berbagai kata kunci

**Hasil yang diharapkan:**
- ✅ Tidak ada error: `Cannot read properties of null (reading 'toLowerCase')`
- ✅ Pencarian berfungsi normal
- ✅ Filter hasil sesuai dengan kata kunci

**Test edge cases:**
- Parent dengan nama null/undefined
- Parent dengan email null/undefined
- Parent dengan phone null/undefined
- Parent dengan occupation null/undefined

### 3. **Test Menu Tambah Kelas**

**Langkah:**
1. Buka `/dashboard/classes`
2. Klik tombol "Tambah Kelas" di kanan atas
3. Isi form tambah kelas
4. Submit form

**Hasil yang diharapkan:**
- ✅ Tombol "Tambah Kelas" dapat diklik
- ✅ Redirect ke halaman `/dashboard/classes/new`
- ✅ Form tambah kelas tampil dengan benar
- ✅ Semua field dapat diisi
- ✅ Submit berhasil dan redirect ke list kelas

**Form fields yang harus ada:**
- ID Kelas (required)
- Nama Kelas (required)
- Tingkat (required)
- Tahun Ajaran (required)
- Wali Kelas (optional)

## Verifikasi Database

### 1. **Cek Tabel Teachers**
```sql
-- Pastikan tabel teachers ada dan memiliki data
SELECT id, name, teacher_id, specialization 
FROM teachers 
LIMIT 5;
```

### 2. **Cek Relasi Homeroom Teacher**
```sql
-- Cek kelas yang memiliki wali kelas
SELECT 
  c.name as class_name,
  c.homeroom_teacher_id,
  t.name as teacher_name
FROM classes c
LEFT JOIN teachers t ON c.homeroom_teacher_id = t.id
WHERE c.homeroom_teacher_id IS NOT NULL;
```

### 3. **Cek Data Parents**
```sql
-- Cek parent yang mungkin memiliki null values
SELECT 
  id,
  name,
  phone,
  email,
  occupation
FROM parents 
WHERE name IS NULL 
   OR phone IS NULL 
   OR email IS NULL 
   OR occupation IS NULL
LIMIT 10;
```

## File yang Diperbaiki

1. **`app/dashboard/classes/[id]/page.tsx`** - Query homeroom teacher
2. **`components/parents/parent-list.tsx`** - Null check untuk search
3. **`components/students/add-parent-form.tsx`** - Null check untuk search
4. **`app/dashboard/classes/new/page.tsx`** - Halaman tambah kelas baru

## Kemungkinan Error Lain

### Error: "teachers table doesn't exist"
**Solusi**: Pastikan tabel teachers sudah dibuat di database

### Error: "homeroom_teacher_id foreign key constraint"
**Solusi**: Pastikan homeroom_teacher_id merujuk ke teachers.id yang valid

### Error: "Form validation failed"
**Solusi**: Pastikan semua field required diisi dengan benar

## Catatan Penting

- **Homeroom teacher** sekarang mengambil data dari tabel `teachers`, bukan `users`
- **Search functionality** sekarang aman dari null values
- **Tambah kelas** sekarang memiliki halaman yang proper
- **Semua perbaikan** backward compatible dengan data existing

Jika masih ada error, periksa browser console dan server log untuk detail lebih lanjut.
