# Panduan Mengatasi Masalah Import dan Edit Kelas

## Masalah yang Ditemukan

### 1. **Data Orang Tua Duplikat saat Import**
- Setiap import membuat parent baru tanpa mengecek duplikasi
- Parent dengan nama dan nomor HP yang sama dibuat berulang kali

### 2. **Error "ID kelas harus disertakan" saat Edit Kelas**
- Form edit kelas masih memvalidasi `class_id` sebagai required
- Seharusnya `class_id` tidak perlu diubah saat edit

## Solusi yang Sudah Diterapkan

### ✅ **Perbaikan 1: Mencegah Duplikasi Parent**

Import script sekarang akan:
1. **Cek parent yang sudah ada** berdasarkan nama dan nomor HP
2. **Gunakan parent yang sudah ada** jika ditemukan
3. **Buat parent baru** hanya jika belum ada

```typescript
// Contoh logic baru di import script
const { data: existingFather } = await supabase
  .from('parents')
  .select('id')
  .eq('name', fatherData.name)
  .eq('phone', fatherData.phone || '')
  .maybeSingle()

if (existingFather) {
  father = existingFather  // Gunakan yang sudah ada
} else {
  // Buat baru jika belum ada
  const { data: newFather } = await supabase
    .from('parents')
    .insert(fatherData)
    .select()
    .single()
}
```

### ✅ **Perbaikan 2: Form Edit Kelas**

Form kelas sekarang:
1. **Menggunakan schema validasi berbeda** untuk create vs edit
2. **Field `class_id` disabled** saat edit mode
3. **Validasi `class_id` optional** saat edit

## Langkah-Langkah Perbaikan

### 1. **Membersihkan Data Parent Duplikat yang Sudah Ada**

Jalankan script berikut di Supabase SQL Editor:

```sql
-- Lihat parent yang duplikat
WITH duplicate_parents AS (
  SELECT 
    name, 
    phone,
    COUNT(*) as count,
    STRING_AGG(id::text, ', ') as parent_ids
  FROM parents 
  WHERE name IS NOT NULL
  GROUP BY name, phone 
  HAVING COUNT(*) > 1
)
SELECT 
  name,
  phone,
  count as duplicate_count,
  parent_ids
FROM duplicate_parents
ORDER BY count DESC;
```

**Jika ada duplikat, jalankan cleanup:**

```sql
-- Update relasi student_parent ke parent yang akan dipertahankan
WITH duplicate_parents AS (
  SELECT 
    id,
    name,
    phone,
    ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
  FROM parents 
  WHERE name IS NOT NULL
),
parent_mapping AS (
  SELECT 
    d1.id as old_parent_id,
    d2.id as new_parent_id
  FROM duplicate_parents d1
  JOIN duplicate_parents d2 ON d1.name = d2.name AND d1.phone = d2.phone
  WHERE d1.rn > 1 AND d2.rn = 1
)
UPDATE student_parent 
SET parent_id = pm.new_parent_id
FROM parent_mapping pm
WHERE student_parent.parent_id = pm.old_parent_id;

-- Hapus parent duplikat
WITH duplicate_parents AS (
  SELECT 
    id,
    name,
    phone,
    ROW_NUMBER() OVER (PARTITION BY name, phone ORDER BY created_at ASC) as rn
  FROM parents 
  WHERE name IS NOT NULL
)
DELETE FROM parents 
WHERE id IN (
  SELECT id 
  FROM duplicate_parents 
  WHERE rn > 1
);
```

### 2. **Test Import Ulang**

Setelah perbaikan:
- Import data yang sama berulang kali
- Parent tidak akan duplikat lagi
- Hanya student baru yang akan dibuat

### 3. **Test Edit Kelas**

Setelah perbaikan:
- Buka halaman edit kelas
- Field "ID Kelas" akan disabled
- Form dapat disimpan tanpa error

## File yang Sudah Diperbaiki

1. **`app/api/students/import/route.ts`** - Logic anti-duplikasi parent
2. **`components/classes/class-form.tsx`** - Form edit kelas yang diperbaiki
3. **`scripts/clean-duplicate-parents.sql`** - Script cleanup duplikat

## Verifikasi Perbaikan

### Cek Import Parent:
```sql
-- Hitung parent sebelum dan sesudah import
SELECT COUNT(*) as total_parents FROM parents;

-- Import data yang sama 2x, jumlah parent seharusnya tidak berubah
```

### Cek Edit Kelas:
1. Buka `/dashboard/classes/[id]/edit`
2. Field "ID Kelas" seharusnya disabled
3. Form dapat disimpan tanpa error "ID kelas harus disertakan"

## Catatan Penting

- **Backup data** sebelum menjalankan script cleanup
- **Test di environment development** dulu sebelum production
- **Parent matching** berdasarkan nama + nomor HP yang exact match
- **Class ID** tetap tidak bisa diubah saat edit (by design)

Kedua masalah seharusnya sudah teratasi dengan perbaikan ini.
