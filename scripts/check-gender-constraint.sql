-- Script untuk memeriksa constraint gender di tabel students
-- Jalankan script ini di Supabase SQL Editor

-- 1. Periksa semua constraint di tabel students
SELECT 'Current constraints on students table:' as info;
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'students'::regclass;

-- 2. Periksa check constraint khusus untuk gender
SELECT 'Gender check constraint details:' as info;
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'students'::regclass 
AND conname LIKE '%gender%';

-- 3. Periksa struktur kolom gender
SELECT 'Gender column details:' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'students' 
AND column_name = 'gender';

-- 4. Jika ada constraint yang membatasi gender, hapus dulu
-- Uncomment baris di bawah jika perlu menghapus constraint
/*
ALTER TABLE students DROP CONSTRAINT IF EXISTS students_gender_check;
*/

-- 5. Buat constraint baru yang lebih fleksibel (jika diperlukan)
-- Uncomment baris di bawah untuk membuat constraint baru
/*
ALTER TABLE students ADD CONSTRAINT students_gender_check 
CHECK (gender IN ('male', 'female', 'L', 'P', 'Laki-laki', 'Perempuan', 'laki-laki', 'perempuan'));
*/

-- 6. Atau buat constraint yang hanya menerima 'male' dan 'female'
-- Uncomment baris di bawah untuk constraint yang ketat
/*
ALTER TABLE students ADD CONSTRAINT students_gender_check 
CHECK (gender IN ('male', 'female') OR gender IS NULL);
*/

-- 7. Test insert dengan berbagai format gender
-- Uncomment untuk test
/*
-- Test dengan format yang berbeda
INSERT INTO students (student_id, name, gender, status) VALUES 
('TEST-GENDER-001', 'Test Male', 'male', 'active'),
('TEST-GENDER-002', 'Test Female', 'female', 'active'),
('TEST-GENDER-003', 'Test L', 'L', 'active'),
('TEST-GENDER-004', 'Test P', 'P', 'active')
ON CONFLICT (student_id) DO NOTHING;

-- Lihat hasil
SELECT student_id, name, gender FROM students WHERE student_id LIKE 'TEST-GENDER%';

-- Hapus data test
DELETE FROM students WHERE student_id LIKE 'TEST-GENDER%';
*/

SELECT 'Gender constraint check completed.' as result;
