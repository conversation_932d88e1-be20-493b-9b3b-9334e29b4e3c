# Panduan Memperbaiki Masalah Import Students

## Masalah
1. Error: "Could not find the 'birth_place' column of 'students' in the schema cache"
2. Error: "new row for relation 'students' violates check constraint 'students_gender_check'"

## Penyebab
1. Tabel `students` di database tidak memiliki kolom yang diperlukan oleh import script:
   - `birth_place`
   - `phone`
   - `email`
   - `entry_date`

2. Constraint gender di database hanya menerima nilai `'male'` dan `'female'`, tapi data Excel menggunakan format lain seperti `'L'`, `'P'`, `'Laki-laki'`, `'Perempuan'`

## Solusi

### 1. Jalankan Script Perbaikan Kolom Database

Buka Supabase SQL Editor dan jalankan script berikut untuk menambahkan kolom yang hilang:

```sql
-- Script untuk memperbaiki masalah import students
-- Menambahkan kolom yang hilang di tabel students

-- 1. Per<PERSON>sa struktur tabel students saat ini
SELECT 'Current students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

-- 2. Tambahkan kolom birth_place jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS birth_place VARCHAR;

-- 3. Tambahkan kolom phone jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS phone VARCHAR;

-- 4. Tambahkan kolom email jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS email VARCHAR;

-- 5. Tambahkan kolom entry_date jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS entry_date DATE;

-- 6. Periksa struktur tabel students setelah update
SELECT 'Updated students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

SELECT 'Schema fix completed. You can now retry the student import.' as result;
```

### 2. Jalankan Script Perbaikan Gender Constraint

Jalankan script berikut untuk memperbaiki constraint gender:

```sql
-- Hapus constraint gender yang ada
DO $$
BEGIN
    ALTER TABLE students DROP CONSTRAINT IF EXISTS students_gender_check;
    ALTER TABLE students DROP CONSTRAINT IF EXISTS check_gender;
END $$;

-- Buat constraint baru yang hanya menerima 'male' dan 'female' atau NULL
ALTER TABLE students ADD CONSTRAINT students_gender_check
CHECK (gender IN ('male', 'female') OR gender IS NULL);

SELECT 'Gender constraint fixed. Only "male", "female", or NULL are now allowed.' as result;
```

### 3. Alternatif: Gunakan Script yang Sudah Dibuat

Jalankan file script yang sudah dibuat:
```bash
# Di Supabase SQL Editor, copy dan paste isi file:
scripts/fix-students-import-schema.sql  # Untuk kolom yang hilang
scripts/fix-gender-constraint.sql       # Untuk constraint gender
```

### 4. Verifikasi Perbaikan

Setelah menjalankan script, pastikan:

**Kolom yang sudah ada di tabel `students`:**
- `birth_place` (VARCHAR)
- `phone` (VARCHAR)
- `email` (VARCHAR)
- `entry_date` (DATE)

**Constraint gender yang benar:**
- Hanya menerima nilai: `'male'`, `'female'`, atau `NULL`

### 5. Format Data Excel yang Didukung

Import script sekarang sudah mendukung berbagai format gender:
- **Untuk Laki-laki**: `L`, `Laki-laki`, `male`, `pria`
- **Untuk Perempuan**: `P`, `Perempuan`, `female`, `wanita`

### 6. Test Import Ulang

Setelah database diperbaiki, coba lakukan import students lagi. Error seharusnya sudah tidak muncul.

## File yang Sudah Diperbaiki

1. `scripts/fix-students-import-schema.sql` - Script perbaikan kolom database
2. `scripts/fix-gender-constraint.sql` - Script perbaikan constraint gender
3. `scripts/check-gender-constraint.sql` - Script untuk memeriksa constraint
4. `app/api/students/import/route.ts` - Import script dengan gender mapping
5. `scripts/fix-database-schema.sql` - Script perbaikan umum (sudah diupdate)
6. `scripts/create-tables-without-fk.sql` - Schema tabel (sudah diupdate)
7. `types/supabase.ts` - TypeScript types (sudah diupdate)

## Catatan

- Script menggunakan `ADD COLUMN IF NOT EXISTS` sehingga aman dijalankan berulang kali
- Kolom baru akan memiliki nilai NULL untuk data yang sudah ada
- Import script sudah mendukung semua kolom yang diperlukan
- Gender mapping otomatis mengkonversi berbagai format ke `'male'` atau `'female'`
- Jika format gender tidak dikenali, akan muncul error dengan pesan yang jelas
- Constraint gender sekarang konsisten dengan form UI yang menggunakan `'male'` dan `'female'`
