# Panduan Memperbaiki Masalah Import Students

## Masalah
Error saat import students: "Could not find the 'birth_place' column of 'students' in the schema cache"

## Penyebab
Tabel `students` di database tidak memiliki kolom yang diperlukan oleh import script:
- `birth_place`
- `phone` 
- `email`
- `entry_date`

## Solusi

### 1. Jalankan Script Perbaikan Database

Buka Supabase SQL Editor dan jalankan script berikut:

```sql
-- Script untuk memperbaiki masalah import students
-- Menambahkan kolom yang hilang di tabel students

-- 1. Periksa struktur tabel students saat ini
SELECT 'Current students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

-- 2. Tambahkan kolom birth_place jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS birth_place VARCHAR;

-- 3. Tambahkan kolom phone jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS phone VARCHAR;

-- 4. Tambahkan kolom email jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS email VARCHAR;

-- 5. Tambahkan kolom entry_date jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS entry_date DATE;

-- 6. Periksa struktur tabel students setelah update
SELECT 'Updated students table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

SELECT 'Schema fix completed. You can now retry the student import.' as result;
```

### 2. Alternatif: Gunakan Script yang Sudah Dibuat

Jalankan file script yang sudah dibuat:
```bash
# Di Supabase SQL Editor, copy dan paste isi file:
scripts/fix-students-import-schema.sql
```

### 3. Verifikasi Perbaikan

Setelah menjalankan script, pastikan kolom-kolom berikut sudah ada di tabel `students`:
- `birth_place` (VARCHAR)
- `phone` (VARCHAR) 
- `email` (VARCHAR)
- `entry_date` (DATE)

### 4. Test Import Ulang

Setelah database diperbaiki, coba lakukan import students lagi. Error "Could not find the 'birth_place' column" seharusnya sudah tidak muncul.

## File yang Sudah Diperbaiki

1. `scripts/fix-students-import-schema.sql` - Script perbaikan database
2. `scripts/fix-database-schema.sql` - Script perbaikan umum (sudah diupdate)
3. `scripts/create-tables-without-fk.sql` - Schema tabel (sudah diupdate)
4. `types/supabase.ts` - TypeScript types (sudah diupdate)

## Catatan

- Script menggunakan `ADD COLUMN IF NOT EXISTS` sehingga aman dijalankan berulang kali
- Kolom baru akan memiliki nilai NULL untuk data yang sudah ada
- Import script sudah mendukung semua kolom yang diperlukan
