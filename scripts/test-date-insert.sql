-- Script untuk test insert tanggal di tabel students
-- Jalankan script ini di Supabase SQL Editor

-- 1. Test insert dengan berbagai format tanggal
INSERT INTO students (
    student_id, 
    name, 
    birth_date, 
    birth_place,
    gender,
    entry_date,
    status
) VALUES 
-- Test dengan tanggal yang sudah dikonversi dari Excel serial 41142
(
    'TEST-DATE-001',
    'Test Student Excel Date',
    '2012-08-20',  -- <PERSON>il konversi dari 41142
    'Jakarta',
    'male',
    '2024-01-15',
    'active'
),
-- Test dengan format tanggal normal
(
    'TEST-DATE-002',
    'Test Student Normal Date',
    '2010-05-15',
    'Bandung',
    'female',
    '2024-01-15',
    'active'
),
-- Test dengan tanggal NULL
(
    'TEST-DATE-003',
    'Test Student Null Date',
    NULL,
    'Surabaya',
    'male',
    '2024-01-15',
    'active'
)
ON CONFLICT (student_id) DO UPDATE SET
    name = EXCLUDED.name,
    birth_date = EXCLUDED.birth_date,
    birth_place = EXCLUDED.birth_place,
    gender = EXCLUDED.gender,
    entry_date = EXCLUDED.entry_date,
    updated_at = NOW();

-- 2. Periksa hasil insert
SELECT 'Test insert results:' as info;
SELECT 
    student_id, 
    name, 
    birth_date,
    birth_place,
    gender,
    entry_date,
    status
FROM students 
WHERE student_id LIKE 'TEST-DATE%'
ORDER BY student_id;

-- 3. Test dengan tanggal yang tidak valid (ini akan gagal)
-- Uncomment untuk test error
/*
INSERT INTO students (student_id, name, birth_date, status) 
VALUES ('TEST-INVALID-DATE', 'Test Invalid Date', '41142', 'active');
*/

-- 4. Hapus data test
DELETE FROM students WHERE student_id LIKE 'TEST-DATE%';

SELECT 'Date insert test completed successfully.' as result;
