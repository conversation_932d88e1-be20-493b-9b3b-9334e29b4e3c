# Test Edit Kelas - Panduan Verifikasi

## Masalah yang Diperbaiki

**Error**: "Gagal mengupdate data kelas: ID kelas wajib diisi"

**Penyebab**: 
1. Form tidak mengirim `id` dalam request body
2. API endpoint hanya mengecek `id` dari body, tidak dari query params
3. Validasi schema masih me<PERSON>an `class_id` saat edit

## Perbaikan yang Diterapkan

### 1. **Form Component** (`components/classes/class-form.tsx`)
- ✅ Menambahkan `id` ke request body saat edit mode
- ✅ Schema validasi berbeda untuk create vs edit
- ✅ Field `class_id` disabled saat edit

### 2. **API Endpoint** (`app/api/classes/route.ts`)
- ✅ Mengecek `id` dari body dan query params
- ✅ Menggunakan `classId` yang benar untuk update

## Langkah Test Manual

### 1. **Buat Kelas Baru** (untuk test edit)
1. Buka `/dashboard/classes`
2. <PERSON><PERSON> "Tambah Kelas"
3. Isi data:
   - ID Kelas: `TEST-EDIT`
   - <PERSON><PERSON>: `Test Edit Kelas`
   - Tingkat: `7`
   - <PERSON><PERSON>: `2024/2025`
4. Simpan

### 2. **Test Edit Kelas**
1. Buka kelas yang baru dibuat
2. Klik "Edit" atau buka `/dashboard/classes/[id]/edit`
3. Verifikasi:
   - ✅ Field "ID Kelas" disabled dan berisi `TEST-EDIT`
   - ✅ Field lain dapat diedit
4. Ubah nama menjadi `Test Edit Kelas - Updated`
5. Klik "Simpan"
6. **Hasil yang diharapkan**: Berhasil disimpan tanpa error

### 3. **Test Browser Console**
Buka Developer Tools dan lihat:

**Request yang dikirim:**
```javascript
// URL: PUT /api/classes?id=uuid-kelas
// Body:
{
  "id": "uuid-kelas",
  "class_id": "TEST-EDIT",
  "name": "Test Edit Kelas - Updated",
  "level": "7",
  "academic_year": "2024/2025",
  "homeroom_teacher_id": null
}
```

**Response yang diharapkan:**
```javascript
{
  "message": "Class updated successfully",
  "data": { ... }
}
```

## Debug Jika Masih Error

### 1. **Cek Console Log**
```javascript
console.log("Submitting class form:", values)
console.log("Processed values:", processedValues)
```

### 2. **Cek Network Tab**
- Method: `PUT`
- URL: `/api/classes?id=uuid-kelas`
- Body harus mengandung `id`

### 3. **Cek Server Log**
```javascript
// Di API endpoint
console.log("Request body:", await request.json())
console.log("Class ID:", classId)
```

## Kemungkinan Error Lain

### Error: "class_id is required"
**Solusi**: Pastikan menggunakan `editClassFormSchema` untuk edit mode

### Error: "Cannot read property 'id'"
**Solusi**: Pastikan `classId` prop dikirim ke `ClassForm`

### Error: "Unauthorized"
**Solusi**: Pastikan user sudah login dan memiliki permission

## Verifikasi Akhir

Setelah perbaikan, edit kelas seharusnya:
- ✅ Tidak error "ID kelas wajib diisi"
- ✅ Field class_id disabled tapi tetap dikirim dalam request
- ✅ Data berhasil diupdate di database
- ✅ Redirect ke halaman detail kelas setelah berhasil

## File yang Diperbaiki

1. `components/classes/class-form.tsx` - Form logic dan validasi
2. `app/api/classes/route.ts` - API endpoint untuk update
3. `scripts/test-class-edit.md` - Panduan test ini

Jika masih ada error, periksa console browser dan server log untuk detail lebih lanjut.
