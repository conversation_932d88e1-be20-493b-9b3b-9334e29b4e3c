# SafeAvatar Component

## 🎯 Overview

SafeAvatar adalah komponen client-side yang mengatasi masalah event handler di Server Components dan menyediakan error handling yang robust untuk menampilkan foto user.

## 🚨 Problem Solved

### Original Error:
```
Error: Event handlers cannot be passed to Client Component props.
<... src=... alt="tes" onError={function onError}>
                                 ^^^^^^^^^^^^^^^^^^
If you need interactivity, consider converting part of this to a Client Component.
```

### Root Cause:
- Next.js Server Components tidak bisa menerima event handlers
- `onError` handler di AvatarImage menyebabkan runtime error
- Perlu komponen client-side untuk handle image loading errors

## ✅ Solution: SafeAvatar Component

### Features:
- ✅ **Client-side rendering** dengan "use client" directive
- ✅ **Error handling** untuk gambar yang gagal load
- ✅ **Automatic fallback** ke placeholder atau initials
- ✅ **Multiple sizes** (sm, md, lg, xl)
- ✅ **Type-safe** dengan TypeScript
- ✅ **Pre-configured variants** untuk Teacher dan Student

## 🛠️ Component Structure

### Base Component: SafeAvatar
```typescript
interface SafeAvatarProps {
  src?: string | null
  alt: string
  fallbackText: string
  className?: string
  fallbackClassName?: string
  size?: "sm" | "md" | "lg" | "xl"
}
```

### Pre-configured Variants:
```typescript
// For Teachers
<TeacherAvatar teacher={teacher} size="md" />

// For Students  
<StudentAvatar student={student} size="lg" />
```

## 📏 Size Options

| Size | Dimensions | Use Case |
|------|------------|----------|
| `sm` | 32x32px | Small lists, compact views |
| `md` | 48x48px | Default size, cards |
| `lg` | 64x64px | Detail pages, headers |
| `xl` | 96x96px | Profile pages, large displays |

## 🎨 Usage Examples

### 1. Teacher List
```typescript
// Before (Error-prone)
<Avatar className="h-12 w-12">
  <AvatarImage 
    src={teacher.photo_url || placeholder} 
    alt={teacher.name}
    onError={handleError} // ❌ Causes Server Component error
  />
  <AvatarFallback>
    {teacher.name.split(' ').map(n => n[0]).join('').toUpperCase()}
  </AvatarFallback>
</Avatar>

// After (Safe)
<TeacherAvatar teacher={teacher} size="md" />
```

### 2. Teacher Detail
```typescript
// Before
<Avatar className="h-16 w-16">
  <AvatarImage src={teacher.photo_url} alt={teacher.name} />
  <AvatarFallback className="text-lg">
    {initials}
  </AvatarFallback>
</Avatar>

// After
<TeacherAvatar teacher={teacher} size="lg" />
```

### 3. Custom Usage
```typescript
<SafeAvatar
  src={user.photo_url}
  alt={user.name}
  fallbackText={getInitials(user.name)}
  size="xl"
  className="border-2 border-emerald-500"
  fallbackClassName="bg-emerald-100 text-emerald-700"
/>
```

## 🔧 Error Handling Logic

### Image Loading States:
1. **Initial**: Show image if src is valid
2. **Loading**: Display image while loading
3. **Error**: Hide image, show fallback
4. **Success**: Display loaded image

### Fallback Hierarchy:
1. **Primary**: User's photo_url
2. **Secondary**: Placeholder SVG
3. **Tertiary**: Initials in AvatarFallback

### Code Logic:
```typescript
const [imageError, setImageError] = useState(false)
const shouldShowImage = src && !imageError && src.trim() !== ""
const imageUrl = shouldShowImage ? src : getPlaceholderUrl(size)
```

## 📁 File Updates

### Files Modified:
- ✅ `components/teachers/teacher-list.tsx` - Uses TeacherAvatar
- ✅ `app/dashboard/teachers/[id]/page.tsx` - Uses TeacherAvatar
- ✅ `components/students/student-detail.tsx` - Removed onError handler

### New Files:
- ✅ `components/ui/safe-avatar.tsx` - Main component
- ✅ `docs/SAFE_AVATAR_COMPONENT.md` - Documentation (this file)

## 🧪 Testing

### Manual Testing:
1. **Valid Image URL**: Should display photo
2. **Invalid Image URL**: Should show placeholder
3. **Null/Empty URL**: Should show initials
4. **Network Error**: Should gracefully fallback

### Test Cases:
```typescript
// Test data
const testCases = [
  {
    name: "Valid Supabase URL",
    photo_url: "https://labs-sptsa.xd1iar.easypanel.host/storage/v1/object/public/photos/teachers/teacher_123.png",
    expected: "Shows actual photo"
  },
  {
    name: "Invalid URL", 
    photo_url: "https://invalid-url.com/image.jpg",
    expected: "Shows placeholder"
  },
  {
    name: "Empty URL",
    photo_url: "",
    expected: "Shows initials"
  },
  {
    name: "Null URL",
    photo_url: null,
    expected: "Shows initials"
  }
]
```

## 🎯 Benefits

### Before SafeAvatar:
- ❌ Server Component errors with event handlers
- ❌ No graceful error handling
- ❌ Inconsistent fallback behavior
- ❌ Repeated code across components

### After SafeAvatar:
- ✅ Client-side rendering prevents errors
- ✅ Robust error handling and fallbacks
- ✅ Consistent behavior across app
- ✅ Reusable and type-safe
- ✅ Easy to maintain and extend

## 🔮 Future Enhancements

### Potential Features:
1. **Loading states** with skeleton
2. **Image optimization** with Next.js Image
3. **Lazy loading** for performance
4. **Cache management** for loaded images
5. **Custom placeholder** generation
6. **Accessibility** improvements

### Usage Patterns:
```typescript
// With loading state
<SafeAvatar 
  src={user.photo_url}
  alt={user.name}
  fallbackText={getInitials(user.name)}
  showLoading={true}
  loadingComponent={<Skeleton />}
/>

// With custom placeholder
<SafeAvatar 
  src={user.photo_url}
  alt={user.name}
  fallbackText={getInitials(user.name)}
  customPlaceholder="/custom-avatar.svg"
/>
```

## 📞 Migration Guide

### For Existing Components:

1. **Import SafeAvatar**:
   ```typescript
   import { TeacherAvatar, StudentAvatar } from "@/components/ui/safe-avatar"
   ```

2. **Replace Avatar usage**:
   ```typescript
   // Old
   <Avatar>
     <AvatarImage src={user.photo_url} alt={user.name} />
     <AvatarFallback>{initials}</AvatarFallback>
   </Avatar>
   
   // New
   <TeacherAvatar teacher={user} size="md" />
   ```

3. **Remove event handlers**:
   ```typescript
   // Remove onError, onLoad handlers
   // SafeAvatar handles these internally
   ```

4. **Update imports**:
   ```typescript
   // Remove if not used elsewhere
   // import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
   ```

## ✅ Result

SafeAvatar component mengatasi masalah Server Component error dan menyediakan:
- 🔧 **Robust error handling** untuk image loading
- 🎨 **Consistent UI** across all avatar displays  
- 🚀 **Better performance** dengan proper client-side rendering
- 📱 **Responsive design** dengan multiple size options
- 🛡️ **Type safety** dengan TypeScript interfaces

Foto teacher dan student sekarang akan ditampilkan dengan benar tanpa runtime errors! 🎉
